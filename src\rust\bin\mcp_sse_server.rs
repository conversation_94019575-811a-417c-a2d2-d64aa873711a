// MCP SSE 服务器入口点 - 支持网络访问
use cunzhi::{mcp::ZhiServer, utils::auto_init_logger, log_important};
use rmcp::{ServiceExt, transport::sse_server::SseServer};
use std::env;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 自动初始化日志系统
    auto_init_logger()?;

    // 从环境变量获取配置
    let host = env::var("MCP_WEB_HOST").unwrap_or_else(|_| "127.0.0.1".to_string());
    let port = env::var("MCP_WEB_PORT")
        .unwrap_or_else(|_| "8765".to_string())
        .parse::<u16>()
        .unwrap_or(8765);

    log_important!(info, "启动 MCP SSE 服务器在 {}:{}", host, port);

    // 创建SSE服务器
    let sse_server = SseServer::new(format!("{}:{}", host, port).parse()?);
    
    // 创建MCP服务器实例
    let mcp_server = ZhiServer::new();
    
    // 启动服务器
    let service = mcp_server.serve(sse_server).await?;
    
    log_important!(info, "MCP SSE 服务器已启动，可通过 http://{}:{}/sse 访问", host, port);
    
    // 等待服务器关闭
    service.waiting().await?;
    Ok(())
}
