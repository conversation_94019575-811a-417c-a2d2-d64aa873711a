name: Build and Publish MCP Package

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            target: x86_64-unknown-linux-gnu
            binary_name: 寸止-sse
          - os: windows-latest
            target: x86_64-pc-windows-msvc
            binary_name: 寸止-sse.exe
          - os: macos-latest
            target: x86_64-apple-darwin
            binary_name: 寸止-sse
          - os: macos-latest
            target: aarch64-apple-darwin
            binary_name: 寸止-sse

    steps:
    - uses: actions/checkout@v4

    - name: Setup Rust
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        target: ${{ matrix.target }}
        override: true

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine

    - name: Cache Cargo dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-${{ matrix.target }}-${{ hashFiles('**/Cargo.lock') }}

    - name: Build Rust binary
      run: |
        cargo build --release --target ${{ matrix.target }} --bin 寸止-sse

    - name: Create binary directory
      run: |
        mkdir -p src/mcp_feedback_enhanced/bin

    - name: Copy binary (Unix)
      if: runner.os != 'Windows'
      run: |
        cp target/${{ matrix.target }}/release/${{ matrix.binary_name }} src/mcp_feedback_enhanced/bin/
        chmod +x src/mcp_feedback_enhanced/bin/${{ matrix.binary_name }}

    - name: Copy binary (Windows)
      if: runner.os == 'Windows'
      run: |
        copy target\${{ matrix.target }}\release\${{ matrix.binary_name }} src\mcp_feedback_enhanced\bin\

    - name: Build Python package
      run: |
        python -m build

    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: package-${{ matrix.os }}-${{ matrix.target }}
        path: dist/

  publish:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')

    steps:
    - uses: actions/checkout@v4

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install twine

    - name: Download all artifacts
      uses: actions/download-artifact@v3

    - name: Publish to PyPI
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
      run: |
        # Combine all packages
        mkdir -p combined_dist
        find . -name "*.whl" -o -name "*.tar.gz" | xargs -I {} cp {} combined_dist/
        
        # Publish to PyPI
        twine upload combined_dist/*
